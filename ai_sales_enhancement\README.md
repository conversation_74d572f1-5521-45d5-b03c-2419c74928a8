# AI Sales Enhancement Module

## Overview

The AI Sales Enhancement module provides comprehensive enhancements to Odoo 18's sales and inventory management system. This module adds intelligent search capabilities, truck tracking, weight display, negative stock handling, and post-confirmation lot/location selection to streamline your sales operations.

## Features

### 🔍 **1. Enhanced Product/Lot Search**
- **Smart Search Bar**: Search by product name or lot number with auto-fill functionality
- **Order-Level Search**: Quick add products to sales orders/quotations from order header
- **Line-Level Search**: Enhanced search in individual order lines
- **Intelligent Matching**: Automatically fills product, lot, and location when exact matches are found
- **Multi-Result Handling**: Shows selection options when multiple matches are found
- **Real-time Feedback**: Instant search results and notifications

### 🚛 **2. Truck Number Tracking**
- **Sales Order Tracking**: Add truck numbers to sales orders/quotations (optional)
- **Delivery Tracking**: Add truck numbers to delivery orders for better logistics management
- **Search by Truck**: Find sales orders and delivery orders by truck number
- **Client Ordering**: Clients can specify preferred truck numbers when placing orders
- **Truck Summary**: View all deliveries assigned to a specific truck
- **Weight Calculations**: Automatic total weight calculation per truck

### ⚖️ **3. Weight Display Enhancement**
- **Lot Weight Info**: Display weight information in all lot selection screens
- **Available Quantity**: Show total available quantity across all locations
- **Location Breakdown**: Detailed breakdown of quantities by location
- **Weight Calculations**: Automatic weight calculations based on product and quantity

### 📦 **4. Negative Stock Management**
- **Allow Negative Stock**: Configure system to allow negative stock in sales
- **Direct Lot Creation**: Create lot numbers directly in delivery notes
- **Quick Lot Wizard**: Streamlined wizard for creating multiple lots at once
- **Auto-generation**: Automatic lot name generation based on product and date

### 🎯 **5. Post-Confirmation Lot/Location Selection**
- **After Sales Confirmation**: Select lot numbers and locations after confirming sales orders
- **Batch Selection**: Handle multiple products in a single wizard
- **Stock Integration**: Automatically update stock moves with selected lots/locations
- **Validation**: Ensure sufficient stock before assignment

## Installation

### Prerequisites
- Odoo 18 Community Edition
- Required modules:
  - `ai_bt_spices_module`
  - `barcode_scanning_sale_purchase`

### Installation Steps
1. Copy the module to your Odoo addons directory
2. Update the app list: `Settings > Apps > Update Apps List`
3. Search for "AI Sales Enhancement"
4. Click Install

### Installation Order
Install in this order to avoid dependency issues:
1. `ai_bt_spices_module`
2. `barcode_scanning_sale_purchase`
3. `ai_sales_enhancement`

## Configuration

### Settings Location
Navigate to: `Settings > Sales > AI Sales Enhancement`

### Available Configurations

#### Enhanced Product Search
- **Enable Enable Enhanced Product Search**: Enable/disable the smart search functionality
- **Auto-fill on Exact Match**: Automatically fill fields when exact matches are found
- **Show Multiple Results**: Display selection options for multiple matches

#### Truck Tracking
- **Enable Truck Tracking**: Enable truck number fields in delivery orders
- **Mandatory Truck Number**: Make truck number required for delivery orders
- **Truck Search Integration**: Enable searching delivery orders by truck number

#### Negative Stock Management
- **Allow Negative Stock Sales**: Permit sales even when stock is negative
- **Enable Quick Lot Creation**: Allow creating lots directly in delivery notes
- **Auto-generate Lot Names**: Automatically generate lot names based on patterns

#### Weight Display
- **Show Weight in Lot Selection**: Display weight information in lot selection screens
- **Location Breakdown**: Show detailed location-wise stock breakdown
- **Weight Calculations**: Enable automatic weight calculations

## Usage Guide

### Enhanced Product Search

#### Order-Level Quick Add (Sales Orders/Quotations):
1. Use the "Add Product/Lot" field in the order header
2. Type either:
   - Product name (partial matching supported)
   - Lot number (exact or partial matching)
3. System will automatically add the product to order lines
4. If multiple matches found, wizard opens for selection
5. Default quantity is 1.0 (can be modified after adding)

#### Line-Level Search (Individual Order Lines):
1. Use the "Product/Lot Search" field in order lines
2. Type either:
   - Product name (partial matching supported)
   - Lot number (exact or partial matching)
3. System will auto-fill if exact match is found
4. Select from options if multiple matches are found

#### Search Examples:
```
Order Level Search:
Search Term: "VARIYALI" → Wizard shows all VARIYALI products
Search Term: "A125E16001" → Automatically adds product with this lot

Line Level Search:
Search Term: "VARIYALI" → Shows all products containing "VARIYALI"
Search Term: "A125E16001" → Auto-fills product associated with this lot number
```

### Truck Number Tracking

#### Adding Truck Numbers to Sales Orders:
1. Open any sales order or quotation
2. Find the "Truck Number" field (optional)
3. Enter the truck number for client preference
4. Truck number will be carried forward to delivery orders

#### Adding Truck Numbers to Delivery Orders:
1. Open any delivery order
2. Find the "Truck Number" field (visible only for outgoing deliveries)
3. Enter the truck number
4. Use "View Truck Deliveries" button to see all deliveries for this truck

#### Searching by Truck:
1. **Sales Orders**: Go to Sales > Orders, use search bar and type truck number
2. **Delivery Orders**: Go to Inventory > Delivery Orders, search by truck number
3. System will find all orders/deliveries assigned to that truck

### Weight Display

#### In Lot Selection:
- Weight information is automatically displayed in format: "Qty: X.XX | Weight: Y.YY kg"
- Total available quantity is shown across all locations
- Location breakdown shows stock distribution

### Negative Stock & Lot Creation

#### Allowing Negative Stock:
1. Enable "Allow Negative Stock Sales" in settings
2. Sales can proceed even when stock is insufficient
3. Use "Quick Lot Creation" in delivery notes to create lots for negative stock

#### Quick Lot Creation:
1. Open a delivery order with products requiring lot tracking
2. Click "Quick Lot Creation" button
3. System shows all products needing lot assignment
4. Choose to create new lots or select existing ones
5. Lot names can be auto-generated or manually entered

### Post-Confirmation Lot Selection

#### After Sales Confirmation:
1. Confirm a sales order containing tracked products
2. Click "Select Lots & Locations" button
3. Wizard opens showing all products requiring lot assignment
4. Select appropriate lots and locations for each product
5. System updates stock moves automatically

## Technical Details

### Models Extended
- `sale.order` - Added lot/location selection wizard action
- `sale.order.line` - Added search fields and lot/location tracking
- `stock.picking` - Added truck number and quick lot creation
- `stock.lot` - Enhanced with weight display and availability info
- `product.product` - Enhanced search functionality
- `res.config.settings` - Added configuration options

### New Models
- `lot.location.selection.wizard` - Post-confirmation lot/location selection
- `quick.lot.creation.wizard` - Quick lot creation for delivery notes
- `product.selection.wizard` - Enhanced product selection with weight info

### Key Methods
- `_onchange_product_lot_search()` - Handles intelligent search
- `action_open_lot_location_wizard()` - Opens post-confirmation wizard
- `action_quick_lot_creation()` - Opens quick lot creation wizard
- `_compute_display_weight_info()` - Calculates weight display information

## Compatibility

### Odoo Version
- **Supported**: Odoo 18 Community Edition
- **Tested**: Odoo 18.0

### Module Dependencies
- **Required**: `sale`, `stock`, `product`
- **Enhanced**: `ai_bt_spices_module`, `barcode_scanning_sale_purchase`

### Database
- **PostgreSQL**: Fully supported
- **MySQL**: Not tested
- **SQLite**: Not recommended for production

## Troubleshooting

### Common Issues

#### Search Not Working
- **Check**: Enhanced Product Search is enabled in settings
- **Verify**: Product has proper name or lot numbers exist
- **Solution**: Clear browser cache and retry

#### Truck Number Not Visible
- **Check**: Truck Tracking is enabled in settings
- **Verify**: You're viewing an outgoing delivery order
- **Solution**: Refresh the page after enabling the setting

#### Weight Not Displaying
- **Check**: Products have weight configured
- **Verify**: Lots exist with proper product associations
- **Solution**: Update product weights in Product > Products

#### Lot Creation Fails
- **Check**: Product tracking is set to "By Lots" or "By Serial Numbers"
- **Verify**: User has proper permissions for stock operations
- **Solution**: Check product configuration and user access rights

### Performance Considerations
- **Large Databases**: Search may be slower with thousands of products/lots
- **Optimization**: Consider adding database indexes for frequently searched fields
- **Monitoring**: Watch for slow queries in database logs

## Support

### Documentation
- **User Manual**: Available in module documentation
- **Technical Guide**: See code comments and docstrings
- **API Reference**: Check model and method documentation

### Getting Help
- **Issues**: Report bugs through your Odoo support channel
- **Features**: Request enhancements through proper channels
- **Customization**: Contact your Odoo implementation partner

## License

This module is licensed under the LGPL-3 license. See LICENSE file for details.

## Credits

**Developed by**: AI Assistant for Odoo 18 Community Edition
**Version**: 1.0.0
**Last Updated**: 2025-07-05

---

*This module enhances your Odoo sales operations with intelligent search, tracking, and inventory management capabilities.*

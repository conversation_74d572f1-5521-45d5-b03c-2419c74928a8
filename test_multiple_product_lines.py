#!/usr/bin/env python3
"""
Test script to verify that the same product can be added multiple times 
with different rates in sale orders.

This script should be run in the Odoo shell context:
python odoo-bin shell -d your_database_name --addons-path=your_addons_path
"""

def test_multiple_product_lines(env):
    """Test creating multiple sale order lines with the same product but different rates"""
    
    print("=== Testing Multiple Product Lines with Different Rates ===")
    
    # Get or create a test partner
    partner = env['res.partner'].search([('name', '=', 'Test Customer')], limit=1)
    if not partner:
        partner = env['res.partner'].create({
            'name': 'Test Customer',
            'is_company': True,
        })
    
    # Get or create a test product
    product = env['product.product'].search([('name', '=', 'Test Product')], limit=1)
    if not product:
        product = env['product.product'].create({
            'name': 'Test Product',
            'type': 'consu',
            'weight': 1.0,  # 1 kg
            'list_price': 100.0,
        })
    
    print(f"Using partner: {partner.name}")
    print(f"Using product: {product.name}")
    
    # Create a sale order
    sale_order = env['sale.order'].create({
        'partner_id': partner.id,
        'state': 'draft',
    })
    
    print(f"Created sale order: {sale_order.name}")
    
    # Create first line with rate 100
    line1 = env['sale.order.line'].create({
        'order_id': sale_order.id,
        'product_id': product.id,
        'product_uom_qty': 10,
        'x_product_rate': 100.0,
        'price_unit': 100.0,
    })
    
    print(f"Created line 1 - Sequence: {line1.line_sequence}, Rate: {line1.x_product_rate}")
    
    # Create second line with rate 120 (same product, different rate)
    line2 = env['sale.order.line'].create({
        'order_id': sale_order.id,
        'product_id': product.id,
        'product_uom_qty': 5,
        'x_product_rate': 120.0,
        'price_unit': 120.0,
    })
    
    print(f"Created line 2 - Sequence: {line2.line_sequence}, Rate: {line2.x_product_rate}")
    
    # Create third line with rate 90 (same product, different rate)
    line3 = env['sale.order.line'].create({
        'order_id': sale_order.id,
        'product_id': product.id,
        'product_uom_qty': 8,
        'x_product_rate': 90.0,
        'price_unit': 90.0,
    })
    
    print(f"Created line 3 - Sequence: {line3.line_sequence}, Rate: {line3.x_product_rate}")
    
    # Verify all lines exist
    all_lines = sale_order.order_line.filtered(lambda l: l.product_id == product)
    print(f"\nTotal lines with the same product: {len(all_lines)}")
    
    for line in all_lines:
        print(f"  Line {line.line_sequence}: Qty={line.product_uom_qty}, Rate={line.x_product_rate}, Price={line.price_unit}")
    
    # Test that sequences are unique and properly assigned
    sequences = all_lines.mapped('line_sequence')
    print(f"\nSequences: {sequences}")
    print(f"Unique sequences: {len(set(sequences)) == len(sequences)}")
    
    print("\n=== Test completed successfully! ===")
    print("Multiple lines with the same product but different rates have been created.")
    
    return sale_order

def test_invoice_creation(env, sale_order):
    """Test that invoices are created properly with multiple lines"""
    
    print("\n=== Testing Invoice Creation ===")
    
    # Confirm the sale order
    sale_order.action_confirm()
    print(f"Sale order confirmed: {sale_order.state}")
    
    # Create invoice
    invoice = sale_order._create_invoices()
    print(f"Invoice created: {invoice.name}")
    
    # Check invoice lines
    invoice_lines = invoice.invoice_line_ids.filtered(lambda l: l.product_id == sale_order.order_line[0].product_id)
    print(f"Invoice lines for the product: {len(invoice_lines)}")
    
    for line in invoice_lines:
        print(f"  Invoice line: Qty={line.quantity}, Price={line.price_unit}, Name={line.name}")
    
    return invoice

if __name__ == '__main__':
    # This will only work if run in Odoo shell context
    try:
        # Test the functionality
        sale_order = test_multiple_product_lines(env)
        
        # Test invoice creation
        invoice = test_invoice_creation(env, sale_order)
        
        print(f"\nSale Order ID: {sale_order.id}")
        print(f"Invoice ID: {invoice.id}")
        
    except NameError:
        print("This script must be run in Odoo shell context.")
        print("Example: python odoo-bin shell -d your_database_name")

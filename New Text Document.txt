  File "/usr/lib/python3/dist-packages/odoo/http.py", line 1962, in _transactioning
    return service_model.retrying(func, env=self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/service/model.py", line 156, in retrying
    result = func()
             ^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 1929, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 2177, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_http.py", line 333, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/http.py", line 727, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/addons/web/controllers/dataset.py", line 42, in call_button
    action = call_kw(request.env[model], method, args, kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/api.py", line 533, in call_kw
    result = getattr(recs, name)(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_module.py", line 75, in check_and_log
    return method(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_module.py", line 480, in button_immediate_install
    return self._button_immediate_function(self.env.registry[self._name].button_install)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/addons/base/models/ir_module.py", line 604, in _button_immediate_function
    registry = modules.registry.Registry.new(self._cr.dbname, update_module=True)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/tools/func.py", line 97, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/modules/registry.py", line 127, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 484, in load_modules
    processed_modules += load_marked_modules(env, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 365, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 228, in load_module_graph
    load_data(env, idref, mode, kind='data', package=package)
  File "/usr/lib/python3/dist-packages/odoo/modules/loading.py", line 72, in load_data
    tools.convert_file(env, package.name, filename, idref, mode, noupdate, kind)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 608, in convert_file
    convert_xml_import(env, module, fp, idref, mode, noupdate)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 679, in convert_xml_import
    obj.parse(doc.getroot())
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 594, in parse
    self._tag_root(de)
  File "/usr/lib/python3/dist-packages/odoo/tools/convert.py", line 548, in _tag_root
    raise ParseError(msg) from None  # Restart with "--log-handler odoo.tools.convert:DEBUG" for complete traceback
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
odoo.tools.convert.ParseError: while parsing /mnt/extra-addons/ai_sales_enhancement/views/stock_lot_views.xml:32
Error while parsing or validating view:

Element '<xpath expr="//field[@name=&#39;quantity&#39;]">' cannot be located in parent view

View error context:
{'file': '/mnt/extra-addons/ai_sales_enhancement/views/stock_lot_views.xml',
 'line': 1,
 'name': 'stock.quant.tree.enhanced',
 'view': ir.ui.view(2294,),
 'view.model': 'stock.quant',
 'view.parent': ir.ui.view(1520,),
 'xmlid': 'view_stock_quant_tree_enhanced'}


The above server error caused the following client error:
RPC_ERROR: Odoo Server Error
    RPC_ERROR
        at makeErrorFromResponse (http://167.86.123.231:8069/web/assets/fbdeb91/web.assets_web.min.js:3156:163)
        at XMLHttpRequest.<anonymous> (http://167.86.123.231:8069/web/assets/fbdeb91/web.assets_web.min.js:3161:13)
# -*- coding: utf-8 -*-

import io
import json
import datetime
import pytz
import base64
from odoo import fields, models, api
from odoo.tools import json_default

try:
    from odoo.tools.misc import xlsxwriter
except ImportError:
    import xlsxwriter


class StockLedgerReport(models.TransientModel):
    """Stock Ledger Report Wizard"""
    
    _name = "stock.ledger.report"
    _description = "Stock Ledger Report"

    date_from = fields.Date(
        string='From Date',
        required=True,
        default=lambda self: fields.Date.today().replace(day=1)
    )
    date_to = fields.Date(
        string='To Date',
        required=True,
        default=fields.Date.today()
    )
    warehouse_ids = fields.Many2many(
        'stock.warehouse',
        string='Warehouses',
        required=True
    )
    product_ids = fields.Many2many(
        'product.product',
        string='Products',
        help="Leave empty to include all products. Products are filtered based on selected warehouses and date range."
    )

    @api.onchange('warehouse_ids', 'date_from', 'date_to')
    def _onchange_warehouse_date_filter_products(self):
        """Filter products based on selected warehouses and date range"""
        if self.warehouse_ids and self.date_from and self.date_to:
            # Get warehouse locations
            warehouse_locations = self.warehouse_ids.mapped('lot_stock_id')
            all_warehouse_locations = []
            for loc in warehouse_locations:
                all_warehouse_locations.extend(self.env['stock.location'].search([('id', 'child_of', loc.id)]).ids)

            # Find products with moves in these warehouses and date range
            domain = [
                ('date', '>=', self.date_from),
                ('date', '<=', self.date_to),
                ('state', '=', 'done'),
                '|',
                ('location_id', 'in', all_warehouse_locations),
                ('location_dest_id', 'in', all_warehouse_locations),
            ]

            moves = self.env['stock.move'].search(domain)
            product_ids = moves.mapped('product_id').ids

            return {
                'domain': {
                    'product_ids': [('id', 'in', product_ids), ('active', '=', True)]
                }
            }
        else:
            # If no warehouse or date selected, show all products
            return {
                'domain': {
                    'product_ids': [('type', 'in', ['product', 'consu']), ('active', '=', True)]
                }
            }

    @api.onchange('warehouse_ids')
    def _onchange_warehouse_clear_products(self):
        """Clear product selection when warehouses change"""
        if self.warehouse_ids:
            # Clear existing product selection to avoid confusion
            self.product_ids = [(5, 0, 0)]  # Clear all selected products









    def export_excel(self):
        """Export stock ledger report to Excel - Direct method without JavaScript"""
        return self.export_excel_direct()

    def export_excel_direct(self):
        """Direct Excel export without JavaScript - Full format matching screenshot"""
        try:
            # Validate required fields
            if not self.warehouse_ids:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': 'Validation Error',
                        'message': "Please select at least one warehouse",
                        'type': 'warning',
                        'sticky': True,
                    }
                }

            # Prepare data
            data = {
                'date_from': self.date_from.strftime('%Y-%m-%d'),
                'date_to': self.date_to.strftime('%Y-%m-%d'),
                'warehouse_ids': self.warehouse_ids.ids,
                'product_ids': self.product_ids.ids if self.product_ids else [],
            }

            # Generate Excel file with full formatting
            output = io.BytesIO()
            workbook = xlsxwriter.Workbook(output, {'in_memory': True})
            sheet = workbook.add_worksheet('Stock Ledger')

            # Define formats
            title_format = workbook.add_format({
                'font_size': 16,
                'bold': True,
                'align': 'center',
                'valign': 'vcenter'
            })

            header_format = workbook.add_format({
                'font_size': 12,
                'bold': True,
                'align': 'center',
                'valign': 'vcenter',
                'border': 1,
                'bg_color': '#D3D3D3'
            })

            cell_format = workbook.add_format({
                'font_size': 10,
                'align': 'center',
                'valign': 'vcenter',
                'border': 1
            })

            cell_left_format = workbook.add_format({
                'font_size': 10,
                'align': 'left',
                'valign': 'vcenter',
                'border': 1
            })

            number_format = workbook.add_format({
                'font_size': 10,
                'align': 'right',
                'valign': 'vcenter',
                'border': 1,
                'num_format': '#,##0.00'
            })

            product_header_format = workbook.add_format({
                'font_size': 12,
                'bold': True,
                'align': 'left',
                'valign': 'vcenter',
                'bg_color': '#E6E6FA'
            })

            # Set column widths to match screenshot
            sheet.set_column('A:A', 12)  # DATE
            sheet.set_column('B:B', 20)  # VOUCHER NO./ACCOUNT
            sheet.set_column('C:C', 15)  # LOT NUMBER
            sheet.set_column('D:D', 10)  # QTY REC
            sheet.set_column('E:E', 10)  # QTY ISS
            sheet.set_column('F:F', 10)  # QTY BAL
            sheet.set_column('G:G', 8)   # UNITS
            sheet.set_column('H:H', 12)  # PER KG COST
            sheet.set_column('I:I', 12)  # AMOUNT
            sheet.set_column('J:J', 8)   # UNIT ISS
            sheet.set_column('K:K', 10)  # RATE
            sheet.set_column('L:L', 12)  # AMOUNT
            sheet.set_column('M:M', 15)  # REMARK
            sheet.set_column('N:N', 10)  # UNITS BAL

            # Title and header information
            warehouse_names = ', '.join(self.warehouse_ids.mapped('name'))

            sheet.merge_range('A1:N1', f'STOCK LEDGER FROM {data["date_from"]} TO {data["date_to"]}', title_format)
            sheet.merge_range('A2:N2', f'GODOWN - {warehouse_names}', title_format)

            # Column headers - Exact match to screenshot
            headers = [
                'DATE', 'VOUCHER NO./ACCOUNT', 'LOT NUMBER', 'QTY REC', 'QTY ISS', 'QTY BAL',
                'UNITS', 'PER KG COST', 'AMOUNT', 'UNIT ISS', 'RATE', 'AMOUNT', 'REMARK', 'UNITS BAL'
            ]

            for col, header in enumerate(headers):
                sheet.write(3, col, header, header_format)

            # Get and write data
            product_moves = self.get_stock_moves_data(data)
            current_row = 4

            for product_name, product_data in product_moves.items():
                # Product header
                sheet.merge_range(current_row, 0, current_row, 13, f'PRODUCT : {product_name}', product_header_format)
                current_row += 1

                # Running balances
                running_qty_balance = 0
                running_weight_balance = 0

                # Product moves
                running_units_balance = 0
                for move in product_data['moves']:
                    running_qty_balance += move['qty_received'] - move['qty_issued']  # odoo_qty * product_weight balance
                    running_units_balance += move['units_received'] - move['units_issued']  # odoo_qty balance

                    # Combine voucher and account as per screenshot
                    voucher_account = f"{move['voucher_no']} / {move['account']}" if move['account'] else move['voucher_no']

                    sheet.write(current_row, 0, move['date'].strftime('%d/%m/%Y'), cell_format)
                    sheet.write(current_row, 1, voucher_account, cell_left_format)
                    sheet.write(current_row, 2, move['lot_number'], cell_left_format)
                    sheet.write(current_row, 3, move['qty_received'] if move['qty_received'] else '', number_format)  # odoo_qty * product_weight
                    sheet.write(current_row, 4, move['qty_issued'] if move['qty_issued'] else '', number_format)      # odoo_qty * product_weight
                    sheet.write(current_row, 5, running_qty_balance, number_format)  # Running balance of odoo_qty * product_weight
                    sheet.write(current_row, 6, move['units_received'] if move['units_received'] else move['units_issued'] if move['units_issued'] else '', number_format)  # odoo_qty
                    sheet.write(current_row, 7, move['per_kg_cost'], number_format)  # PER KG COST
                    sheet.write(current_row, 8, move['amount'], number_format)  # AMOUNT
                    sheet.write(current_row, 9, move['units_issued'] if move['units_issued'] else '', number_format)  # UNIT ISS (odoo_qty)
                    sheet.write(current_row, 10, move['rate'], number_format)  # RATE
                    sheet.write(current_row, 11, move['amount'], number_format)  # AMOUNT (repeated)
                    sheet.write(current_row, 12, move['remark'], cell_left_format)  # REMARK
                    sheet.write(current_row, 13, running_units_balance, number_format)  # UNITS BAL (odoo_qty balance)

                    current_row += 1

                # Product total
                total_format = workbook.add_format({
                    'font_size': 10,
                    'bold': True,
                    'align': 'center',
                    'valign': 'vcenter',
                    'border': 1,
                    'bg_color': '#FFFF99'
                })

                sheet.write(current_row, 0, 'TOTAL', total_format)
                sheet.write(current_row, 1, '', total_format)
                sheet.write(current_row, 2, '', total_format)
                sheet.write(current_row, 3, product_data['total_qty_received'], total_format)  # Total odoo_qty * product_weight received
                sheet.write(current_row, 4, product_data['total_qty_issued'], total_format)    # Total odoo_qty * product_weight issued
                sheet.write(current_row, 5, running_qty_balance, total_format)  # Final balance odoo_qty * product_weight
                sheet.write(current_row, 6, product_data.get('total_units_received', 0), total_format)  # Total odoo_qty received
                sheet.write(current_row, 7, '', total_format)  # PER KG COST
                sheet.write(current_row, 8, product_data['total_amount'], total_format)  # AMOUNT
                sheet.write(current_row, 9, product_data.get('total_units_issued', 0), total_format)  # Total odoo_qty issued
                sheet.write(current_row, 10, '', total_format)  # RATE
                sheet.write(current_row, 11, product_data['total_amount'], total_format)  # AMOUNT
                sheet.write(current_row, 12, '', total_format)  # REMARK
                sheet.write(current_row, 13, running_units_balance, total_format)  # Final balance odoo_qty

                current_row += 2  # Add space between products

            workbook.close()
            output.seek(0)

            # Return as attachment for direct download
            filename = f"stock_ledger_{self.date_from.strftime('%Y%m%d')}_{self.date_to.strftime('%Y%m%d')}.xlsx"
            attachment = self.env['ir.attachment'].create({
                'name': filename,
                'type': 'binary',
                'datas': base64.b64encode(output.read()),
                'res_model': self._name,
                'res_id': self.id,
                'mimetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            })

            return {
                'type': 'ir.actions.act_url',
                'url': f'/web/content/{attachment.id}?download=true',
                'target': 'self',
            }

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error in direct Excel export: {str(e)}")

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Export Error',
                    'message': f"Error generating report: {str(e)}",
                    'type': 'danger',
                    'sticky': True,
                }
            }

    def get_stock_moves_data(self, data):
        """Get stock moves data for the report"""
        date_from = data.get('date_from')
        date_to = data.get('date_to')
        warehouse_ids = data.get('warehouse_ids', [])
        product_ids = data.get('product_ids', [])

        # Build domain for stock moves
        domain = [
            ('date', '>=', date_from),
            ('date', '<=', date_to),
            ('state', '=', 'done'),
        ]

        # Add warehouse filter - include both source and destination locations
        if warehouse_ids:
            warehouse_locations = self.env['stock.warehouse'].browse(warehouse_ids).mapped('lot_stock_id')
            all_warehouse_locations = []
            for loc in warehouse_locations:
                all_warehouse_locations.extend(self.env['stock.location'].search([('id', 'child_of', loc.id)]).ids)
            domain.append('|')
            domain.append(('location_id', 'in', all_warehouse_locations))
            domain.append(('location_dest_id', 'in', all_warehouse_locations))

        # Add product filter
        if product_ids:
            domain.append(('product_id', 'in', product_ids))

        # Get stock moves
        stock_moves = self.env['stock.move'].search(domain, order='product_id, date, id')

        # Group moves by product
        product_moves = {}
        for move in stock_moves:
            # Create product key with weight info if available
            product_name = move.product_id.name
            if move.product_id.default_code:
                product_key = f"{product_name} [{move.product_id.default_code}]"
            else:
                product_key = product_name

            if product_key not in product_moves:
                product_moves[product_key] = {
                    'product': move.product_id,
                    'moves': [],
                    'total_qty_received': 0,
                    'total_qty_issued': 0,
                    'total_amount': 0,
                }

            # Determine if it's incoming or outgoing based on warehouse locations
            qty_received = 0
            qty_issued = 0
            weight_received = 0
            weight_issued = 0
            account_name = ''

            # Check if this is a warehouse-related move
            warehouse_locations = []
            if warehouse_ids:
                warehouse_locations = self.env['stock.warehouse'].browse(warehouse_ids).mapped('lot_stock_id')
                all_warehouse_locations = []
                for loc in warehouse_locations:
                    all_warehouse_locations.extend(self.env['stock.location'].search([('id', 'child_of', loc.id)]).ids)

            # Calculate quantities as per your business logic:
            # QTY columns = odoo_qty * product_weight (total weight)
            # UNITS columns = odoo_qty (actual Odoo quantity, not UOM)
            odoo_qty = move.product_uom_qty
            product_weight = move.product_id.weight if move.product_id.weight else 1.0
            calculated_qty = odoo_qty * product_weight  # This goes in QTY columns
            units_value = odoo_qty  # This goes in UNITS columns

            # Determine direction based on location types and warehouse involvement
            if move.location_dest_id.id in all_warehouse_locations and move.location_id.id not in all_warehouse_locations:
                # Incoming to warehouse (receipt)
                qty_received = calculated_qty  # odoo_qty * product_weight
                units_received = units_value   # odoo_qty
                account_name = move.location_id.name
            elif move.location_id.id in all_warehouse_locations and move.location_dest_id.id not in all_warehouse_locations:
                # Outgoing from warehouse (delivery)
                qty_issued = calculated_qty    # odoo_qty * product_weight
                units_issued = units_value     # odoo_qty
                account_name = move.location_dest_id.name
            elif move.location_id.id in all_warehouse_locations and move.location_dest_id.id in all_warehouse_locations:
                # Internal transfer within warehouse
                if move.location_id.usage == 'internal' and move.location_dest_id.usage == 'internal':
                    # For internal moves, show as both received and issued
                    qty_received = calculated_qty  # odoo_qty * product_weight
                    qty_issued = calculated_qty    # odoo_qty * product_weight
                    units_received = units_value   # odoo_qty
                    units_issued = units_value     # odoo_qty
                    account_name = f"{move.location_id.name} → {move.location_dest_id.name}"

            # Skip moves that don't involve the selected warehouses
            if qty_received == 0 and qty_issued == 0:
                continue

            # Get lot information
            lot_numbers = []
            if move.move_line_ids:
                lot_numbers = [line.lot_id.name for line in move.move_line_ids if line.lot_id]
            lot_number = ', '.join(lot_numbers) if lot_numbers else ''

            # Calculate rate and per kg cost
            rate = move.price_unit or move.product_id.standard_price
            amount = move.product_uom_qty * rate

            # Calculate per kg cost (cost per unit weight)
            per_kg_cost = 0

            # Method 1: Use product weight if available
            product_weight = move.product_id.weight
            if product_weight and product_weight > 0:
                per_kg_cost = rate / product_weight
            else:
                # Method 2: Check if UOM is weight-based
                uom_category = move.product_uom.category_id.name.lower() if move.product_uom.category_id else ''
                if 'weight' in uom_category or 'kg' in move.product_uom.name.lower():
                    # If UOM is already weight-based, rate is already per kg
                    per_kg_cost = rate
                else:
                    # Method 3: Use standard conversion or default
                    # For products like "JEERA KACHO NO.1 [50KG]", extract weight from name
                    product_name = move.product_id.name.upper()
                    import re
                    weight_match = re.search(r'\[(\d+)KG\]', product_name)
                    if weight_match:
                        package_weight = float(weight_match.group(1))
                        per_kg_cost = rate / package_weight
                    else:
                        # Default: assume rate is per unit, use product weight or 1
                        per_kg_cost = rate / (product_weight or 1.0)

            # Get voucher number
            voucher_no = ''
            if move.picking_id:
                voucher_no = move.picking_id.name
            elif move.reference:
                voucher_no = move.reference
            elif move.origin:
                voucher_no = move.origin

            # Calculate units values for UNITS columns (should be odoo_qty)
            units_received = units_received if 'units_received' in locals() else 0
            units_issued = units_issued if 'units_issued' in locals() else 0

            move_data = {
                'date': move.date,
                'voucher_no': voucher_no,
                'account': account_name,
                'lot_number': lot_number,
                'qty_received': qty_received,      # odoo_qty * product_weight
                'qty_issued': qty_issued,          # odoo_qty * product_weight
                'units_received': units_received,  # odoo_qty
                'units_issued': units_issued,      # odoo_qty
                'per_kg_cost': per_kg_cost,
                'rate': rate,
                'amount': amount,
                'remark': move.origin or move.reference or '',
            }

            product_moves[product_key]['moves'].append(move_data)
            product_moves[product_key]['total_qty_received'] += qty_received      # Sum of odoo_qty * product_weight
            product_moves[product_key]['total_qty_issued'] += qty_issued          # Sum of odoo_qty * product_weight
            product_moves[product_key]['total_units_received'] = product_moves[product_key].get('total_units_received', 0) + units_received  # Sum of odoo_qty
            product_moves[product_key]['total_units_issued'] = product_moves[product_key].get('total_units_issued', 0) + units_issued        # Sum of odoo_qty
            product_moves[product_key]['total_amount'] += amount

        return product_moves

    def get_xlsx_report(self, data, response):
        """Generate Excel report for stock ledger"""
        try:
            # Validate input data
            if not data:
                raise ValueError("No data provided for report generation")

            # Parse JSON data if it's a string
            if isinstance(data, str):
                data = json.loads(data)

            output = io.BytesIO()
            workbook = xlsxwriter.Workbook(output, {'in_memory': True})
            sheet = workbook.add_worksheet('Stock Ledger')

            # Define formats
            title_format = workbook.add_format({
                'font_size': 16,
                'bold': True,
                'align': 'center',
                'valign': 'vcenter'
            })

            header_format = workbook.add_format({
                'font_size': 12,
                'bold': True,
                'align': 'center',
                'valign': 'vcenter',
                'border': 1,
                'bg_color': '#D3D3D3'
            })

            cell_format = workbook.add_format({
                'font_size': 10,
                'align': 'center',
                'valign': 'vcenter',
                'border': 1
            })

            cell_left_format = workbook.add_format({
                'font_size': 10,
                'align': 'left',
                'valign': 'vcenter',
                'border': 1
            })

            number_format = workbook.add_format({
                'font_size': 10,
                'align': 'right',
                'valign': 'vcenter',
                'border': 1,
                'num_format': '#,##0.00'
            })

            product_header_format = workbook.add_format({
                'font_size': 12,
                'bold': True,
                'align': 'left',
                'valign': 'vcenter',
                'bg_color': '#E6E6FA'
            })

            # Set column widths
            sheet.set_column('A:A', 12)  # DATE
            sheet.set_column('B:B', 15)  # VOUCHER NO.
            sheet.set_column('C:C', 15)  # ACCOUNT
            sheet.set_column('D:D', 15)  # LOT NUMBER
            sheet.set_column('E:E', 10)  # QTY REC
            sheet.set_column('F:F', 10)  # QTY ISS
            sheet.set_column('G:G', 10)  # QTY BAL
            sheet.set_column('H:H', 8)   # UNITS
            sheet.set_column('I:I', 12)  # PER KG COST
            sheet.set_column('J:J', 12)  # AMOUNT
            sheet.set_column('K:K', 8)   # UNIT ISS
            sheet.set_column('L:L', 10)  # RATE
            sheet.set_column('M:M', 12)  # AMOUNT
            sheet.set_column('N:N', 15)  # REMARK
            sheet.set_column('O:O', 10)  # UNITS BAL

            # Title and header information
            date_from = data.get('date_from')
            date_to = data.get('date_to')
            warehouse_names = ', '.join(self.env['stock.warehouse'].browse(data.get('warehouse_ids', [])).mapped('name'))

            sheet.merge_range('A1:O1', f'STOCK LEDGER FROM {date_from} TO {date_to}', title_format)
            sheet.merge_range('A2:O2', f'GODOWN - {warehouse_names}', title_format)

            # Column headers - exact match to screenshot
            headers = [
                'DATE', 'VOUCHER NO./ACCOUNT', 'LOT NUMBER', 'QTY REC', 'QTY ISS', 'QTY BAL',
                'UNITS', 'PER KG COST', 'AMOUNT', 'UNIT ISS', 'RATE', 'AMOUNT', 'REMARK', 'UNITS BAL'
            ]

            for col, header in enumerate(headers):
                sheet.write(3, col, header, header_format)

            # Get data and write to sheet
            product_moves = self.get_stock_moves_data(data)
            current_row = 4

            for product_name, product_data in product_moves.items():
                # Product header
                sheet.merge_range(current_row, 0, current_row, 14, f'PRODUCT : {product_name}', product_header_format)
                current_row += 1

                # Running balance
                running_balance = 0

                # Product moves
                for move in product_data['moves']:
                    running_balance += move['qty_received'] - move['qty_issued']

                    sheet.write(current_row, 0, move['date'].strftime('%d/%m/%Y'), cell_format)
                    sheet.write(current_row, 1, move['voucher_no'], cell_left_format)
                    sheet.write(current_row, 2, move['account'], cell_left_format)
                    sheet.write(current_row, 3, move['lot_number'], cell_left_format)
                    sheet.write(current_row, 4, move['qty_received'] if move['qty_received'] else '', number_format)
                    sheet.write(current_row, 5, move['qty_issued'] if move['qty_issued'] else '', number_format)
                    sheet.write(current_row, 6, running_balance, number_format)
                    sheet.write(current_row, 7, move['units'], cell_format)
                    sheet.write(current_row, 8, move['per_kg_cost'], number_format)  # PER KG COST
                    sheet.write(current_row, 9, move['amount'], number_format)
                    sheet.write(current_row, 10, '', cell_format)  # UNIT ISS
                    sheet.write(current_row, 11, move['rate'], number_format)  # RATE
                    sheet.write(current_row, 12, move['amount'], number_format)
                    sheet.write(current_row, 13, move['remark'], cell_left_format)
                    sheet.write(current_row, 14, running_balance, number_format)

                    current_row += 1

                # Product total
                total_format = workbook.add_format({
                    'font_size': 10,
                    'bold': True,
                    'align': 'center',
                    'valign': 'vcenter',
                    'border': 1,
                    'bg_color': '#FFFF99'
                })

                sheet.write(current_row, 0, 'TOTAL', total_format)
                sheet.write(current_row, 1, '', total_format)
                sheet.write(current_row, 2, '', total_format)
                sheet.write(current_row, 3, '', total_format)
                sheet.write(current_row, 4, product_data['total_qty_received'], total_format)
                sheet.write(current_row, 5, product_data['total_qty_issued'], total_format)
                sheet.write(current_row, 6, running_balance, total_format)
                sheet.write(current_row, 7, '', total_format)
                sheet.write(current_row, 8, '', total_format)
                sheet.write(current_row, 9, product_data['total_amount'], total_format)
                sheet.write(current_row, 10, '', total_format)
                sheet.write(current_row, 11, '', total_format)
                sheet.write(current_row, 12, product_data['total_amount'], total_format)
                sheet.write(current_row, 13, '', total_format)
                sheet.write(current_row, 14, '', total_format)

                current_row += 2  # Add space between products

            workbook.close()
            output.seek(0)
            response.stream.write(output.read())
            output.close()

        except Exception as e:
            # Log the error
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error generating Excel report: {str(e)}")

            # Create a simple error response
            error_output = io.BytesIO()
            error_workbook = xlsxwriter.Workbook(error_output, {'in_memory': True})
            error_sheet = error_workbook.add_worksheet('Error')
            error_sheet.write(0, 0, f'Error generating report: {str(e)}')
            error_workbook.close()
            error_output.seek(0)
            response.stream.write(error_output.read())
            error_output.close()

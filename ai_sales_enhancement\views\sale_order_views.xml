<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Enhanced Sale Order Form View -->
    <record id="view_order_form_enhanced" model="ir.ui.view">
        <field name="name">sale.order.form.enhanced</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <!-- Add Lot/Location Selection Button -->
            <xpath expr="//button[@name='action_confirm']" position="after">
                <button name="action_open_lot_location_wizard" 
                        string="Select Lots &amp; Locations" 
                        type="object" 
                        class="btn-secondary"
                        invisible="state not in ['sale', 'done']"
                        help="Select lot numbers and locations for tracked products"/>
            </xpath>
        </field>
    </record>

    <!-- Enhanced Sale Order Line List View -->
    <record id="view_order_line_tree_enhanced" model="ir.ui.view">
        <field name="name">sale.order.line.tree.enhanced</field>
        <field name="model">sale.order.line</field>
        <field name="inherit_id" ref="sale.view_order_line_tree"/>
        <field name="arch" type="xml">
            <!-- Add enhanced search and lot/location fields -->
            <xpath expr="//field[@name='product_id']" position="before">
                <field name="product_lot_search" 
                       string="Product/Lot Search"
                       placeholder="Search by product name or lot number..."
                       optional="show"/>
            </xpath>
            
            <xpath expr="//field[@name='product_uom_qty']" position="after">
                <field name="selected_lot_id" 
                       string="Selected Lot"
                       optional="show"/>
                <field name="selected_location_id" 
                       string="Selected Location"
                       optional="show"/>
                <field name="available_qty_at_location" 
                       string="Available Qty"
                       optional="show"/>
                <field name="lot_weight" 
                       string="Lot Weight (kg)"
                       optional="show"/>
            </xpath>
        </field>
    </record>

    <!-- Enhanced Sale Order Line Form View -->
    <record id="view_sale_order_line_form_enhanced" model="ir.ui.view">
        <field name="name">sale.order.line.form.enhanced</field>
        <field name="model">sale.order.line</field>
        <field name="inherit_id" ref="sale.view_order_line_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_id']" position="before">
                <group string="Enhanced Product Search">
                    <field name="product_lot_search" 
                           placeholder="Search by product name or lot number..."
                           help="Type product name or lot number for intelligent search"/>
                    <button name="action_open_lot_selection_wizard" 
                            string="Select Lot &amp; Location" 
                            type="object" 
                            class="btn-secondary"
                            invisible="not product_id"
                            help="Open wizard to select lot and location"/>
                </group>
            </xpath>
            
            <xpath expr="//field[@name='product_uom_qty']" position="after">
                <group string="Lot and Location Information" col="2">
                    <field name="selected_lot_id" 
                           domain="[('product_id', '=', product_id)]"
                           options="{'no_create': True}"/>
                    <field name="selected_location_id" 
                           domain="[('usage', '=', 'internal')]"
                           options="{'no_create': True}"/>
                    <field name="available_qty_at_location" readonly="1"/>
                    <field name="lot_weight" readonly="1"/>
                </group>
            </xpath>
        </field>
    </record>

    <!-- Enhanced Sale Order Line Search View -->
    <record id="view_sales_order_line_filter_enhanced" model="ir.ui.view">
        <field name="name">sale.order.line.search.enhanced</field>
        <field name="model">sale.order.line</field>
        <field name="inherit_id" ref="sale.view_sales_order_line_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_id']" position="after">
                <field name="product_lot_search" string="Product/Lot Search"/>
                <field name="selected_lot_id" string="Lot Number"/>
                <field name="selected_location_id" string="Location"/>
            </xpath>
            
            <xpath expr="//group[@name='groupby']" position="inside">
                <filter string="Selected Lot" name="group_by_lot" 
                        context="{'group_by': 'selected_lot_id'}"/>
                <filter string="Selected Location" name="group_by_location" 
                        context="{'group_by': 'selected_location_id'}"/>
            </xpath>
        </field>
    </record>
</odoo>

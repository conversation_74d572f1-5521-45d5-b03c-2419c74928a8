<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Enhanced Sale Order Form View -->
    <record id="view_order_form_enhanced" model="ir.ui.view">
        <field name="name">sale.order.form.enhanced</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <!-- Add Truck Number Field -->
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="truck_number"
                       placeholder="Enter truck number (optional)"
                       help="Specify truck number for this order"/>
            </xpath>

            <!-- Add Enhanced Product Search -->
            <xpath expr="//field[@name='order_line']" position="before">
                <group string="Quick Add Products">
                    <field name="order_product_search"
                           placeholder="Search by product name or lot number to add to order..."
                           help="Type product name or lot number to quickly add products to this order"/>
                </group>
            </xpath>

            <!-- Add enhanced search fields to order lines -->
            <xpath expr="//field[@name='order_line']/list/field[@name='product_id']" position="before">
                <field name="product_lot_search"
                       string="Product/Lot Search"
                       placeholder="Search by product name or lot number..."
                       optional="show"/>
            </xpath>

            <xpath expr="//field[@name='order_line']/list/field[@name='product_uom_qty']" position="after">
                <field name="selected_lot_id"
                       string="Selected Lot"
                       optional="show"/>
                <field name="selected_location_id"
                       string="Selected Location"
                       optional="show"/>
                <field name="available_qty_at_location"
                       string="Available Qty"
                       optional="show"/>
                <field name="lot_weight"
                       string="Lot Weight (kg)"
                       optional="show"/>
            </xpath>

            <!-- Add Lot/Location Selection Button -->
            <xpath expr="//button[@name='action_confirm']" position="after">
                <button name="action_open_lot_location_wizard"
                        string="Select Lots &amp; Locations"
                        type="object"
                        class="btn-secondary"
                        invisible="state not in ['sale', 'done']"
                        help="Select lot numbers and locations for tracked products"/>
            </xpath>
        </field>
    </record>



    <!-- Enhanced Sale Order Search View -->
    <record id="view_order_filter_enhanced" model="ir.ui.view">
        <field name="name">sale.order.search.enhanced</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_sales_order_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="truck_number" string="Truck Number"/>
            </xpath>
            <xpath expr="//group" position="inside">
                <filter string="Has Truck Number"
                        name="has_truck_number"
                        domain="[('truck_number', '!=', False)]"/>
            </xpath>
        </field>
    </record>


</odoo>

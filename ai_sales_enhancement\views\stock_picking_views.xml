<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Enhanced Stock Picking Form View -->
    <record id="view_picking_form_enhanced" model="ir.ui.view">
        <field name="name">stock.picking.form.enhanced</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <!-- Add Truck Number Field -->
            <xpath expr="//field[@name='origin']" position="after">
                <field name="truck_number" 
                       placeholder="Enter truck number for delivery tracking"
                       invisible="picking_type_code != 'outgoing'"/>
            </xpath>
            
            <!-- Add Quick Lot Creation Button -->
            <xpath expr="//button[@name='button_validate']" position="before">
                <button name="action_quick_lot_creation" 
                        string="Quick Lot Creation" 
                        type="object" 
                        class="btn-secondary"
                        invisible="picking_type_code != 'outgoing' or state not in ['draft', 'waiting', 'confirmed', 'assigned']"
                        help="Create lot numbers for products requiring lot tracking"/>
                <button name="action_view_truck_deliveries" 
                        string="View Truck Deliveries" 
                        type="object" 
                        class="btn-secondary"
                        invisible="not truck_number or picking_type_code != 'outgoing'"
                        help="View all deliveries for this truck"/>
            </xpath>
        </field>
    </record>



    <!-- Enhanced Stock Picking Search View -->
    <record id="view_picking_internal_search_enhanced" model="ir.ui.view">
        <field name="name">stock.picking.search.enhanced</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_internal_search"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='origin']" position="after">
                <field name="truck_number" string="Truck Number"/>
            </xpath>
            
            <xpath expr="//group[@name='groupby']" position="inside">
                <filter string="Truck Number" name="group_by_truck" 
                        context="{'group_by': 'truck_number'}"
                        invisible="context.get('picking_type_code') != 'outgoing'"/>
            </xpath>
            
            <xpath expr="//filter[@name='available']" position="after">
                <separator/>
                <filter string="With Truck Number" name="with_truck" 
                        domain="[('truck_number', '!=', False)]"
                        invisible="context.get('picking_type_code') != 'outgoing'"/>
                <filter string="Without Truck Number" name="without_truck" 
                        domain="[('truck_number', '=', False)]"
                        invisible="context.get('picking_type_code') != 'outgoing'"/>
            </xpath>
        </field>
    </record>

    <!-- Enhanced Stock Move Line Form View -->
    <record id="view_move_line_form_enhanced" model="ir.ui.view">
        <field name="name">stock.move.line.form.enhanced</field>
        <field name="model">stock.move.line</field>
        <field name="inherit_id" ref="stock.view_move_line_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='lot_id']" position="after">
                <button name="action_assign_lot_with_negative_stock" 
                        string="Assign/Create Lot" 
                        type="object" 
                        class="btn-secondary"
                        invisible="product_id.tracking not in ['lot', 'serial'] or lot_id"
                        help="Assign or create lot number for this product"/>
            </xpath>
        </field>
    </record>

    <!-- Enhanced Stock Move Line List View -->
    <record id="view_move_line_tree_enhanced" model="ir.ui.view">
        <field name="name">stock.move.line.tree.enhanced</field>
        <field name="model">stock.move.line</field>
        <field name="inherit_id" ref="stock.view_move_line_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='lot_id']" position="after">
                <button name="action_assign_lot_with_negative_stock" 
                        string="Assign/Create" 
                        type="object" 
                        class="btn-link"
                        invisible="product_id.tracking not in ['lot', 'serial'] or lot_id"
                        help="Assign or create lot number"/>
            </xpath>
        </field>
    </record>

    <!-- Truck Delivery Report Action -->
    <record id="action_truck_delivery_report" model="ir.actions.act_window">
        <field name="name">Truck Delivery Report</field>
        <field name="res_model">stock.picking</field>
        <field name="view_mode">list,form</field>
        <field name="domain">[('picking_type_code', '=', 'outgoing'), ('truck_number', '!=', False)]</field>
        <field name="context">{'search_default_group_by_truck': 1}</field>
        <field name="help">View and manage deliveries grouped by truck number</field>
    </record>

    <!-- Menu Item for Truck Delivery Report -->
    <menuitem id="menu_truck_delivery_report"
              name="Truck Delivery Report"
              parent="stock.menu_stock_warehouse_mgmt"
              action="action_truck_delivery_report"
              sequence="50"/>
</odoo>

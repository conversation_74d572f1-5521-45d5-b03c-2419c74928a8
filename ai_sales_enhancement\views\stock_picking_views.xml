<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Enhanced Stock Picking Form View -->
    <record id="view_picking_form_enhanced" model="ir.ui.view">
        <field name="name">stock.picking.form.enhanced</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <!-- Add Truck Number Field -->
            <xpath expr="//field[@name='origin']" position="after">
                <field name="truck_number" 
                       placeholder="Enter truck number for delivery tracking"
                       invisible="picking_type_code != 'outgoing'"/>
            </xpath>
            
            <!-- Add Quick Lot Creation Button -->
            <xpath expr="//button[@name='button_validate']" position="before">
                <button name="action_quick_lot_creation" 
                        string="Quick Lot Creation" 
                        type="object" 
                        class="btn-secondary"
                        invisible="picking_type_code != 'outgoing' or state not in ['draft', 'waiting', 'confirmed', 'assigned']"
                        help="Create lot numbers for products requiring lot tracking"/>
                <button name="action_view_truck_deliveries" 
                        string="View Truck Deliveries" 
                        type="object" 
                        class="btn-secondary"
                        invisible="not truck_number or picking_type_code != 'outgoing'"
                        help="View all deliveries for this truck"/>
            </xpath>
        </field>
    </record>







    <!-- Truck Delivery Report Action -->
    <record id="action_truck_delivery_report" model="ir.actions.act_window">
        <field name="name">Truck Delivery Report</field>
        <field name="res_model">stock.picking</field>
        <field name="view_mode">list,form</field>
        <field name="domain">[('picking_type_code', '=', 'outgoing'), ('truck_number', '!=', False)]</field>
        <field name="help">View and manage deliveries grouped by truck number</field>
    </record>

    <!-- Menu Item for Truck Delivery Report -->
    <menuitem id="menu_truck_delivery_report"
              name="Truck Delivery Report"
              parent="stock.menu_stock_warehouse_mgmt"
              action="action_truck_delivery_report"
              sequence="50"/>
</odoo>

#!/usr/bin/env python3
"""
Test script for stock move rate update functionality.
This script verifies that updating stock move rates correctly updates the rate field
without incorrectly updating the unit price field.

Run this in Odoo shell:
python odoo-bin shell -d your_database_name --addons-path=your_addons_path
exec(open('test_stock_move_rate_update.py').read())
"""

def test_stock_move_rate_update(env):
    """Test the stock move rate update functionality"""
    print("=== Testing Stock Move Rate Update Functionality ===")
    
    # Create test data
    print("Creating test data...")
    
    # Create a test product with weight
    product = env['product.product'].create({
        'name': 'Test Spice Product',
        'type': 'product',
        'weight': 2.0,  # 2 kg per unit
        'standard_price': 100.0,
        'other_material': True,  # Mark as other material
    })
    
    # Create a manufacturing order
    mo = env['mrp.production'].create({
        'product_id': product.id,
        'product_qty': 10,
        'product_uom_id': product.uom_id.id,
    })
    
    # Create a stock move for other material
    move = env['stock.move'].create({
        'name': 'Test Move',
        'product_id': product.id,
        'product_uom_qty': 5,
        'product_uom': product.uom_id.id,
        'location_id': env.ref('stock.stock_location_stock').id,
        'location_dest_id': env.ref('stock.stock_location_customers').id,
        'other_material_production_id': mo.id,
        'other_material': True,
        'by_product_rate': 50.0,  # Initial rate: 50 per kg
        'actual_cost': 50.0,
    })
    
    print(f"Created move {move.id} for product {product.name}")
    print(f"Initial values:")
    print(f"  - actual_cost: {move.actual_cost}")
    print(f"  - by_product_rate: {move.by_product_rate}")
    print(f"  - price_unit: {move.price_unit}")
    print(f"  - product weight: {product.weight}")
    
    # Test the rate update wizard
    print("\nTesting rate update wizard...")
    
    wizard = env['update.stock.move.rate'].create({
        'production_id': mo.id,
        'move_id': move.id,
        'new_rate': 75.0,  # New rate: 75 per kg
        'update_valuation': False,  # Skip valuation update for this test
    })
    
    print(f"Created wizard with new_rate: {wizard.new_rate}")
    
    # Execute the rate update
    result = wizard.action_update_rate()
    
    # Refresh the move to get updated values
    move.refresh()
    
    print(f"\nAfter rate update:")
    print(f"  - actual_cost: {move.actual_cost}")
    print(f"  - by_product_rate: {move.by_product_rate}")
    print(f"  - price_unit: {move.price_unit}")
    print(f"  - user_modified_rate: {move.user_modified_rate}")
    
    # Verify the results
    print("\nVerifying results...")
    
    # Check that actual_cost was updated to new rate
    assert move.actual_cost == 75.0, f"Expected actual_cost=75.0, got {move.actual_cost}"
    print("✅ actual_cost correctly updated to 75.0")
    
    # Check that by_product_rate was updated to new rate (for other materials)
    assert move.by_product_rate == 75.0, f"Expected by_product_rate=75.0, got {move.by_product_rate}"
    print("✅ by_product_rate correctly updated to 75.0")
    
    # Check that price_unit was calculated correctly (rate * weight)
    expected_price_unit = 75.0 * product.weight  # 75 * 2 = 150
    assert abs(move.price_unit - expected_price_unit) < 0.01, f"Expected price_unit={expected_price_unit}, got {move.price_unit}"
    print(f"✅ price_unit correctly calculated as {move.price_unit} (rate * weight = 75 * 2)")
    
    # Check that user_modified_rate flag was set
    assert move.user_modified_rate == True, f"Expected user_modified_rate=True, got {move.user_modified_rate}"
    print("✅ user_modified_rate flag correctly set to True")
    
    print("\n=== Test completed successfully! ===")
    print("The rate update now correctly updates the rate field without incorrectly updating unit price.")
    
    return move, wizard

def test_byproduct_rate_update(env):
    """Test rate update for by-products"""
    print("\n=== Testing By-Product Rate Update ===")
    
    # Create a by-product
    byproduct = env['product.product'].create({
        'name': 'Test By-Product',
        'type': 'product',
        'weight': 1.5,  # 1.5 kg per unit
        'standard_price': 80.0,
    })
    
    # Create a manufacturing order
    mo = env['mrp.production'].create({
        'product_id': byproduct.id,
        'product_qty': 10,
        'product_uom_id': byproduct.uom_id.id,
    })
    
    # Create a stock move for by-product
    move = env['stock.move'].create({
        'name': 'Test By-Product Move',
        'product_id': byproduct.id,
        'product_uom_qty': 3,
        'product_uom': byproduct.uom_id.id,
        'location_id': env.ref('stock.stock_location_stock').id,
        'location_dest_id': env.ref('stock.stock_location_customers').id,
        'production_id': mo.id,
        'sale_product': True,  # Mark as sale product (by-product)
        'by_product_rate': 60.0,  # Initial rate: 60 per kg
        'actual_cost': 60.0,
    })
    
    print(f"Created by-product move {move.id}")
    print(f"Initial values:")
    print(f"  - actual_cost: {move.actual_cost}")
    print(f"  - by_product_rate: {move.by_product_rate}")
    print(f"  - price_unit: {move.price_unit}")
    
    # Test the rate update wizard
    wizard = env['update.stock.move.rate'].create({
        'production_id': mo.id,
        'move_id': move.id,
        'new_rate': 85.0,  # New rate: 85 per kg
        'update_valuation': False,
    })
    
    # Execute the rate update
    wizard.action_update_rate()
    
    # Refresh and verify
    move.refresh()
    
    print(f"\nAfter rate update:")
    print(f"  - actual_cost: {move.actual_cost}")
    print(f"  - by_product_rate: {move.by_product_rate}")
    print(f"  - price_unit: {move.price_unit}")
    
    # Verify results
    assert move.actual_cost == 85.0, f"Expected actual_cost=85.0, got {move.actual_cost}"
    assert move.by_product_rate == 85.0, f"Expected by_product_rate=85.0, got {move.by_product_rate}"
    
    # For by-products, price_unit should be rate * weight
    expected_price_unit = 85.0 * byproduct.weight  # 85 * 1.5 = 127.5
    assert abs(move.price_unit - expected_price_unit) < 0.01, f"Expected price_unit={expected_price_unit}, got {move.price_unit}"
    
    print("✅ By-product rate update test passed!")
    
    return move

if __name__ == '__main__':
    try:
        # Test other material rate update
        move1, wizard1 = test_stock_move_rate_update(env)
        
        # Test by-product rate update
        move2 = test_byproduct_rate_update(env)
        
        print(f"\n🎉 All tests passed!")
        print(f"Other material move ID: {move1.id}")
        print(f"By-product move ID: {move2.id}")
        
    except NameError:
        print("❌ This script must be run in Odoo shell context.")
        print("Example: python odoo-bin shell -d your_database_name")
        print("Then run: exec(open('test_stock_move_rate_update.py').read())")
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

from odoo import api, fields, models, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    def action_open_lot_location_wizard(self):
        """Open wizard for lot number and location selection after confirmation"""
        self.ensure_one()
        
        if self.state not in ['sale', 'done']:
            raise UserError(_('This action is only available for confirmed sales orders.'))
        
        # Get all sale order lines that need lot/location assignment
        lines_needing_assignment = self.order_line.filtered(
            lambda l: l.product_id.tracking in ['lot', 'serial'] and l.product_uom_qty > 0
        )
        
        if not lines_needing_assignment:
            raise UserError(_('No products requiring lot number assignment found in this order.'))
        
        return {
            'name': _('Select Lot Numbers and Locations'),
            'type': 'ir.actions.act_window',
            'res_model': 'lot.location.selection.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_sale_order_id': self.id,
                'default_sale_order_line_ids': [(6, 0, lines_needing_assignment.ids)],
            }
        }


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    # Enhanced search field for product/lot search
    product_lot_search = fields.Char(
        string='Product/Lot Search',
        help="Search by product name or lot number. System will auto-fill product, lot, and location."
    )
    
    # Fields to store selected lot and location
    selected_lot_id = fields.Many2one(
        'stock.lot',
        string='Selected Lot',
        help="Lot number selected for this line"
    )
    
    selected_location_id = fields.Many2one(
        'stock.location',
        string='Selected Location',
        help="Location selected for this line"
    )
    
    # Display fields for better UX
    available_qty_at_location = fields.Float(
        string='Available Qty',
        compute='_compute_available_qty_at_location',
        help="Available quantity at selected location"
    )
    
    lot_weight = fields.Float(
        string='Lot Weight',
        compute='_compute_lot_weight',
        help="Total weight of the selected lot"
    )

    @api.depends('selected_lot_id', 'selected_location_id')
    def _compute_available_qty_at_location(self):
        """Compute available quantity at selected location for selected lot"""
        for line in self:
            if line.selected_lot_id and line.selected_location_id:
                quants = self.env['stock.quant'].search([
                    ('product_id', '=', line.product_id.id),
                    ('lot_id', '=', line.selected_lot_id.id),
                    ('location_id', '=', line.selected_location_id.id),
                ])
                line.available_qty_at_location = sum(quants.mapped('quantity'))
            else:
                line.available_qty_at_location = 0.0

    @api.depends('selected_lot_id')
    def _compute_lot_weight(self):
        """Compute total weight of selected lot"""
        for line in self:
            if line.selected_lot_id:
                line.lot_weight = line.selected_lot_id.weight
            else:
                line.lot_weight = 0.0

    @api.onchange('product_lot_search')
    def _onchange_product_lot_search(self):
        """Enhanced search functionality for product/lot search"""
        if not self.product_lot_search:
            self.product_id = False
            self.selected_lot_id = False
            self.selected_location_id = False
            return

        search_term = self.product_lot_search.strip()
        
        # First, try to find by lot number
        lots = self.env['stock.lot'].search([
            ('name', 'ilike', search_term)
        ], limit=10)
        
        if lots:
            # If exact match found, auto-select
            exact_lot = lots.filtered(lambda l: l.name.lower() == search_term.lower())
            if len(exact_lot) == 1:
                self._auto_select_lot_and_location(exact_lot[0])
                return
            
            # If multiple lots found, show selection
            if len(lots) > 1:
                return self._show_lot_selection_notification(lots)
        
        # If no lots found, search by product name
        products = self.env['product.product'].search([
            '|',
            ('name', 'ilike', search_term),
            ('default_code', 'ilike', search_term)
        ], limit=10)
        
        if products:
            # If exact match found, auto-select
            exact_product = products.filtered(
                lambda p: p.name.lower() == search_term.lower() or 
                         (p.default_code and p.default_code.lower() == search_term.lower())
            )
            if len(exact_product) == 1:
                self.product_id = exact_product[0]
                self._find_best_lot_for_product(exact_product[0])
                return
            
            # If multiple products found, auto-select first one
            if len(products) == 1:
                self.product_id = products[0]
                self._find_best_lot_for_product(products[0])
                return

    def _auto_select_lot_and_location(self, lot):
        """Auto-select lot and find best location with stock"""
        self.product_id = lot.product_id
        self.selected_lot_id = lot
        
        # Find location with highest stock for this lot
        quants = self.env['stock.quant'].search([
            ('product_id', '=', lot.product_id.id),
            ('lot_id', '=', lot.id),
            ('quantity', '>', 0),
        ], order='quantity desc', limit=1)
        
        if quants:
            self.selected_location_id = quants[0].location_id
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Auto-Selected'),
                'message': _('Product: %s, Lot: %s, Location: %s') % (
                    lot.product_id.name, lot.name, 
                    quants[0].location_id.name if quants else _('No stock location')
                ),
                'type': 'success',
            }
        }

    def _find_best_lot_for_product(self, product):
        """Find best available lot for selected product"""
        if product.tracking not in ['lot', 'serial']:
            return
        
        # Find lots with available stock
        quants = self.env['stock.quant'].search([
            ('product_id', '=', product.id),
            ('quantity', '>', 0),
            ('lot_id', '!=', False),
        ], order='quantity desc', limit=1)
        
        if quants:
            self.selected_lot_id = quants[0].lot_id
            self.selected_location_id = quants[0].location_id

    def _show_lot_selection_notification(self, lots):
        """Show notification when multiple lots are found"""
        lot_names = ', '.join(lots.mapped('name')[:5])
        if len(lots) > 5:
            lot_names += f' and {len(lots) - 5} more...'
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Multiple Lots Found'),
                'message': _('Found lots: %s. Please be more specific or use the lot selection wizard.') % lot_names,
                'type': 'info',
            }
        }

    def action_open_lot_selection_wizard(self):
        """Open wizard to select from available lots"""
        self.ensure_one()
        
        if not self.product_id:
            raise UserError(_('Please select a product first.'))
        
        return {
            'name': _('Select Lot and Location'),
            'type': 'ir.actions.act_window',
            'res_model': 'lot.location.selection.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_sale_order_line_ids': [(6, 0, [self.id])],
                'default_product_id': self.product_id.id,
            }
        }

# Stock Move Rate Update Fix

## Problem Description
The stock move rate update wizard was incorrectly updating both the rate field (`actual_cost`) and the unit price field (`price_unit`) with the same value. This caused confusion because:

- **Rate** should be the cost per weight unit (e.g., per kg)
- **Unit Price** should be the total price per product unit (rate × weight)

## Root Cause
In `ai_bt_spices_module/wizards/update_stock_move_rate.py`, line 55 was setting both fields to the same value:

```python
# BEFORE (Incorrect):
move.write({
    'actual_cost': self.new_rate,
    'price_unit': self.new_rate,  # ❌ Wrong! Should be calculated
    'user_modified_rate': True
})
```

## Solution
The fix involves:

1. **Remove direct `price_unit` update** - Let the system calculate it automatically
2. **Update appropriate rate fields** based on move type:
   - `actual_cost` for all moves (this is the main rate field)
   - `by_product_rate` for by-products and other materials
3. **Let existing logic handle `price_unit`** - The `write` method in `StockMove` already has proper logic

```python
# AFTER (Correct):
update_vals = {
    'actual_cost': self.new_rate,
    'user_modified_rate': True
}

# For by-products and other materials, also update by_product_rate
if (move.sale_product or move.riclin_product or move.clean_product or
    move.waste_product or move.other_material):
    update_vals['by_product_rate'] = self.new_rate

move.write(update_vals)  # Let system calculate price_unit
```

## How It Works Now

### For Other Materials:
- `actual_cost` = new rate (e.g., 75 per kg)
- `by_product_rate` = new rate (e.g., 75 per kg)  
- `price_unit` = rate × weight (e.g., 75 × 2kg = 150 per unit) ✅

### For By-Products:
- `actual_cost` = new rate (e.g., 85 per kg)
- `by_product_rate` = new rate (e.g., 85 per kg)
- `price_unit` = rate × weight (e.g., 85 × 1.5kg = 127.5 per unit) ✅

### For Raw Materials:
- `actual_cost` = new rate (e.g., 60 per kg)
- `price_unit` = calculated based on existing logic ✅

## Existing Logic Integration
The fix leverages existing logic in `StockMove.write()` method that properly handles the relationship between fields:

```python
# For other materials (lines 288-302):
if 'by_product_rate' in vals and vals['by_product_rate'] > 0:
    vals['price_unit'] = vals['by_product_rate'] * self.product_id.weight

# For by-products (lines 313-319):
elif 'by_product_rate' in vals and vals['by_product_rate'] > 0:
    if not self.waste_product:
        vals['price_unit'] = vals['by_product_rate'] * self.product_id.weight
```

## Testing
A test script `test_stock_move_rate_update.py` has been created to verify:

1. ✅ `actual_cost` is updated to new rate
2. ✅ `by_product_rate` is updated for by-products/other materials  
3. ✅ `price_unit` is calculated correctly (rate × weight)
4. ✅ `user_modified_rate` flag is set
5. ✅ Different move types are handled correctly

## Files Modified
- `ai_bt_spices_module/wizards/update_stock_move_rate.py` (lines 51-66)

## Files Added
- `test_stock_move_rate_update.py` (test script)
- `STOCK_MOVE_RATE_UPDATE_FIX.md` (this documentation)

## Impact
- ✅ Rate updates now correctly update rate fields without corrupting unit prices
- ✅ Maintains compatibility with existing cost calculation logic
- ✅ Proper field relationships are preserved
- ✅ No breaking changes to existing functionality

## Usage
The wizard now works correctly:
1. Select a stock move in a Manufacturing Order
2. Enter the new rate (per weight unit)
3. Click "Update Rate"
4. System updates rate and calculates unit price automatically

The rate field will show the per-weight cost, and the unit price will show the total cost per product unit.

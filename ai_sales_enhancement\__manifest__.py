{
    'name': 'AI Sales Enhancement',
    'version': '********.0',
    'category': 'Sales',
    'summary': 'Enhanced Sales Module with Advanced Search, Truck Tracking, Weight Display, and Negative Stock Support',
    'description': """
        AI Sales Enhancement Module
        ==========================
        
        This module provides comprehensive enhancements to the Odoo sales system:
        
        **Key Features:**
        
        1. **Advanced Product/Lot Search**
           - Search bar for product name or lot number with auto-fill
           - Intelligent product selection based on lot numbers
           - Automatic location and lot number population
        
        2. **Truck Number Tracking**
           - Add truck number field to delivery notes
           - Search delivery notes by truck number
           - Enhanced delivery tracking capabilities
        
        3. **Weight Display in Lot Selection**
           - Show weight information in lot selection screens
           - Enhanced visibility for purchase, sales, transfer, and MO operations
           - Weight-based decision making support
        
        4. **Negative Stock Support**
           - Allow negative stock for pre-manufacturing sales
           - Direct lot number creation in delivery notes
           - Streamlined sales process for made-to-order items
        
        5. **Post-Confirmation Lot Selection**
           - Lot number and location selection after sales confirmation
           - Flexible inventory allocation
           - Enhanced sales workflow management
        
        **Technical Features:**
        - Seamless integration with existing barcode scanning modules
        - Maintains compatibility with ai_bt_spices_module
        - Enhanced search and filtering capabilities
        - Improved user experience for sales operations
    """,
    'author': 'AI Assistant',
    'website': '',
    'depends': [
        'base',
        'sale',
        'sale_management', 
        'stock',
        'product',
        'ai_bt_spices_module',
        'barcode_scanning_sale_purchase',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/sale_order_views.xml',
        'views/stock_picking_views.xml',
        'views/product_lot_selection_wizard_views.xml',
        'views/stock_lot_views.xml',
        'views/res_config_settings_views.xml',
        'wizards/lot_location_selection_wizard_views.xml',
        'wizards/quick_lot_creation_wizard_views.xml',
    ],
    'demo': [],
    'images': [],
    'license': 'LGPL-3',
    'installable': True,
    'auto_install': False,
    'application': False,
}
